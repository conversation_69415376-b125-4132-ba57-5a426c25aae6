<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <!-- Static file handling for CSS/JS assets -->
    <staticContent>
      <mimeMap fileExtension=".css" mimeType="text/css" />
      <mimeMap fileExtension=".js" mimeType="application/javascript" />
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".woff" mimeType="font/woff" />
      <mimeMap fileExtension=".woff2" mimeType="font/woff2" />
      <mimeMap fileExtension=".ttf" mimeType="font/ttf" />
      <mimeMap fileExtension=".eot" mimeType="application/vnd.ms-fontobject" />
      <mimeMap fileExtension=".svg" mimeType="image/svg+xml" />
    </staticContent>

    <!-- URL Compression for better performance -->
    <urlCompression doStaticCompression="true" doDynamicCompression="true" />

    <!-- Rewrite rules for Laravel routing -->
    <rewrite>
      <rules>
        <!-- Static files rule - serve directly without rewriting -->
        <rule name="Static Files" stopProcessing="true">
          <match url="^(assets|css|js|images|fonts|uploads)/.*" />
          <action type="None" />
        </rule>

        <!-- Laravel routing rule - only for non-static files -->
        <rule name="Laravel Routes" stopProcessing="true">
          <match url="^(.*)$" />
          <conditions logicalGrouping="MatchAll">
            <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
            <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
            <add input="{REQUEST_URI}" pattern="^/(assets|css|js|images|fonts|uploads)/" negate="true" />
          </conditions>
          <action type="Rewrite" url="index.php" />
        </rule>
      </rules>
    </rewrite>
    <tracing>
      <traceFailedRequests>
        <clear />
      </traceFailedRequests>
    </tracing>
    <security>
      <requestFiltering>
        <requestLimits><headerLimits><add header="maxAllowedContentLength" sizeLimit="********" /><add header="maxUrl" sizeLimit="1000" /><add header="MaxFieldLength" sizeLimit="10000" /></headerLimits>1
        </requestLimits>
      </requestFiltering>
    </security>
    <httpErrors errorMode="Detailed">
      <remove statusCode="400" />
      <error statusCode="400" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_request.html" />
      <remove statusCode="401" />
      <error statusCode="401" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unauthorized.html" />
      <remove statusCode="403" />
      <error statusCode="403" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\forbidden.html" />
      <remove statusCode="404" />
      <error statusCode="404" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_found.html" />
      <remove statusCode="405" />
      <error statusCode="405" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\method_not_allowed.html" />
      <remove statusCode="406" />
      <error statusCode="406" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_acceptable.html" />
      <remove statusCode="407" />
      <error statusCode="407" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\proxy_authentication_required.html" />
      <remove statusCode="412" />
      <error statusCode="412" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\precondition_failed.html" />
      <remove statusCode="414" />
      <error statusCode="414" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\request-uri_too_long.html" />
      <remove statusCode="415" />
      <error statusCode="415" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\unsupported_media_type.html" />
      <remove statusCode="500" />
      <error statusCode="500" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\internal_server_error.html" />
      <remove statusCode="501" />
      <error statusCode="501" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\not_implemented.html" />
      <remove statusCode="502" />
      <error statusCode="502" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\bad_gateway.html" />
      <remove statusCode="503" />
      <error statusCode="503" path="C:\Inetpub\vhosts\mybrokerforex.com\error_docs\maintenance.html" />
    </httpErrors>
        <handlers accessPolicy="Read, Execute, Script">
            <remove name="PythonScript" />
            <add name="PythonScript" path="*.py" verb="*" modules="FastCgiModule" scriptProcessor="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" resourceType="File" requireAccess="Script" />
        </handlers>
  </system.webServer>
  <system.web>
    <compilation tempDirectory="C:\Inetpub\vhosts\mybrokerforex.com\tmp" />
  </system.web>
</configuration>
