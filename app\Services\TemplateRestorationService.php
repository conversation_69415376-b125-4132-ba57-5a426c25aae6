<?php

namespace App\Services;

use App\Models\NotificationTemplate;
use App\Models\GeneralSetting;
use Illuminate\Support\Facades\Log;

/**
 * Template Restoration Service
 * 
 * Ensures all existing email templates are properly formatted and compatible
 * with the new Visual Builder system while maintaining backward compatibility.
 */
class TemplateRestorationService
{
    /**
     * Restore all notification templates to ensure Visual Builder compatibility
     */
    public function restoreAllTemplates(): array
    {
        $results = [
            'restored' => 0,
            'errors' => 0,
            'templates' => []
        ];

        $templates = NotificationTemplate::all();
        
        foreach ($templates as $template) {
            try {
                $this->restoreTemplate($template);
                $results['restored']++;
                $results['templates'][] = [
                    'id' => $template->id,
                    'name' => $template->name,
                    'act' => $template->act,
                    'status' => 'restored'
                ];
                
                Log::info("Template restored: {$template->name} (ID: {$template->id})");
            } catch (\Exception $e) {
                $results['errors']++;
                $results['templates'][] = [
                    'id' => $template->id,
                    'name' => $template->name,
                    'act' => $template->act,
                    'status' => 'error',
                    'error' => $e->getMessage()
                ];
                
                Log::error("Template restoration failed: {$template->name} (ID: {$template->id}) - {$e->getMessage()}");
            }
        }

        return $results;
    }

    /**
     * Restore a single notification template
     */
    public function restoreTemplate(NotificationTemplate $template): bool
    {
        // Ensure email_body is not null
        if (is_null($template->email_body)) {
            $template->email_body = $this->getDefaultEmailTemplate($template);
        }

        // Ensure subject is not null
        if (is_null($template->subj)) {
            $template->subj = $this->getDefaultSubject($template);
        }

        // Ensure shortcodes are properly formatted
        if (is_null($template->shortcodes) || empty($template->shortcodes)) {
            $template->shortcodes = json_encode($this->getDefaultShortcodes($template));
        }

        // Clean and format email body for basic structure
        $template->email_body = $this->ensureBasicEmailStructure($template->email_body);

        // Ensure email and SMS status are set
        if (is_null($template->email_status)) {
            $template->email_status = 1;
        }
        
        if (is_null($template->sms_status)) {
            $template->sms_status = 1;
        }

        return $template->save();
    }

    /**
     * Get default email template based on template action
     */
    private function getDefaultEmailTemplate(NotificationTemplate $template): string
    {
        $defaultTemplates = [
            'PASS_RESET_CODE' => $this->getPasswordResetTemplate(),
            'PASS_RESET_DONE' => $this->getPasswordResetDoneTemplate(),
            'EVER_CODE' => $this->getEmailVerificationTemplate(),
            'ADMIN_SUPPORT_REPLY' => $this->getSupportReplyTemplate(),
            'DEPOSIT_COMPLETE' => $this->getDepositCompleteTemplate(),
            'DEPOSIT_APPROVE' => $this->getDepositApproveTemplate(),
            'DEPOSIT_REJECT' => $this->getDepositRejectTemplate(),
            'WITHDRAW_APPROVE' => $this->getWithdrawApproveTemplate(),
            'WITHDRAW_REJECT' => $this->getWithdrawRejectTemplate(),
            'BALANCE_ADD' => $this->getBalanceAddTemplate(),
            'BALANCE_SUBTRACT' => $this->getBalanceSubtractTemplate(),
            'KYC_APPROVE' => $this->getKycApproveTemplate(),
            'KYC_REJECT' => $this->getKycRejectTemplate(),
            'DEFAULT' => $this->getGenericTemplate()
        ];

        return $defaultTemplates[$template->act] ?? $defaultTemplates['DEFAULT'];
    }

    /**
     * Get default subject based on template action
     */
    private function getDefaultSubject(NotificationTemplate $template): string
    {
        $defaultSubjects = [
            'PASS_RESET_CODE' => 'Password Reset Request - {{site_name}}',
            'PASS_RESET_DONE' => 'Password Reset Successful - {{site_name}}',
            'EVER_CODE' => 'Email Verification Required - {{site_name}}',
            'ADMIN_SUPPORT_REPLY' => 'Support Ticket Reply - {{site_name}}',
            'DEPOSIT_COMPLETE' => 'Deposit Completed - {{site_name}}',
            'DEPOSIT_APPROVE' => 'Deposit Approved - {{site_name}}',
            'DEPOSIT_REJECT' => 'Deposit Rejected - {{site_name}}',
            'WITHDRAW_APPROVE' => 'Withdrawal Approved - {{site_name}}',
            'WITHDRAW_REJECT' => 'Withdrawal Rejected - {{site_name}}',
            'BALANCE_ADD' => 'Balance Added - {{site_name}}',
            'BALANCE_SUBTRACT' => 'Balance Deducted - {{site_name}}',
            'KYC_APPROVE' => 'KYC Verification Approved - {{site_name}}',
            'KYC_REJECT' => 'KYC Verification Rejected - {{site_name}}',
            'DEFAULT' => 'Notification from {{site_name}}'
        ];

        return $defaultSubjects[$template->act] ?? $defaultSubjects['DEFAULT'];
    }

    /**
     * Get default shortcodes for a template
     */
    private function getDefaultShortcodes(NotificationTemplate $template): array
    {
        $commonShortcodes = [
            'fullname' => 'Full name of the user',
            'username' => 'Username of the user',
            'email' => 'Email address of the user',
            'site_name' => 'Name of the website',
            'site_url' => 'URL of the website'
        ];

        $specificShortcodes = [
            'PASS_RESET_CODE' => ['code' => 'Password reset verification code'],
            'EVER_CODE' => ['code' => 'Email verification code'],
            'DEPOSIT_COMPLETE' => ['amount' => 'Deposit amount', 'currency' => 'Currency', 'transaction_id' => 'Transaction ID'],
            'DEPOSIT_APPROVE' => ['amount' => 'Deposit amount', 'currency' => 'Currency', 'transaction_id' => 'Transaction ID'],
            'DEPOSIT_REJECT' => ['amount' => 'Deposit amount', 'currency' => 'Currency', 'reason' => 'Rejection reason'],
            'WITHDRAW_APPROVE' => ['amount' => 'Withdrawal amount', 'currency' => 'Currency', 'transaction_id' => 'Transaction ID'],
            'WITHDRAW_REJECT' => ['amount' => 'Withdrawal amount', 'currency' => 'Currency', 'reason' => 'Rejection reason'],
            'BALANCE_ADD' => ['amount' => 'Added amount', 'currency' => 'Currency', 'new_balance' => 'New balance'],
            'BALANCE_SUBTRACT' => ['amount' => 'Deducted amount', 'currency' => 'Currency', 'new_balance' => 'New balance'],
            'KYC_APPROVE' => ['date' => 'Approval date'],
            'KYC_REJECT' => ['reason' => 'Rejection reason', 'date' => 'Rejection date']
        ];

        return array_merge($commonShortcodes, $specificShortcodes[$template->act] ?? []);
    }

    /**
     * Ensure basic email structure
     */
    private function ensureBasicEmailStructure(string $content): string
    {
        // Remove any dangerous scripts or styles
        $content = strip_tags($content, '<p><br><strong><b><em><i><u><h1><h2><h3><h4><h5><h6><ul><ol><li><a><img><div><span><table><tr><td><th><thead><tbody>');
        
        // Ensure proper HTML structure
        if (!str_contains($content, '<p>') && !str_contains($content, '<div>') && !str_contains($content, '<h')) {
            // Wrap plain text in paragraphs
            $lines = explode("\n", $content);
            $content = '';
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $content .= '<p>' . $line . '</p>';
                }
            }
        }

        // Ensure we have some content
        if (empty(trim(strip_tags($content)))) {
            $content = '<p>Dear {{fullname}},</p><p>This is a notification from {{site_name}}.</p><p>Best regards,<br>{{site_name}} Team</p>';
        }

        return $content;
    }

    /**
     * Restore global email template
     */
    public function restoreGlobalTemplate(): bool
    {
        $general = GeneralSetting::first();
        
        if (!$general) {
            return false;
        }

        // Ensure global template has content
        if (is_null($general->email_template) || empty(trim($general->email_template))) {
            $general->email_template = $this->getDefaultGlobalTemplate();
        }

        // Ensure template has basic structure
        $general->email_template = $this->ensureBasicEmailStructure($general->email_template);

        return $general->save();
    }

    /**
     * Get default global email template
     */
    private function getDefaultGlobalTemplate(): string
    {
        return '
        <div style="font-family: Arial, sans-serif; width: 100%; margin: 0 auto; background: #ffffff;">
            <div style="background: #dc3545; color: #ffffff; padding: 20px; text-align: center;">
                <h1 style="margin: 0; font-size: 24px;">{{site_name}}</h1>
            </div>
            
            <div style="padding: 30px;">
                {{message}}
            </div>
            
            <div style="background: #343a40; color: #ffffff; padding: 20px; text-align: center; font-size: 12px;">
                <p style="margin: 0;">© ' . date('Y') . ' {{site_name}}. All rights reserved.</p>
                <p style="margin: 5px 0 0 0;">
                    <a href="{{site_url}}" style="color: #ffffff;">Visit Website</a>
                </p>
            </div>
        </div>';
    }

    // Template methods for different notification types
    private function getPasswordResetTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Password Reset Request</h2>
            <p>Dear {{fullname}},</p>
            <p>You have requested to reset your password. Please use the following verification code:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                <strong style="font-size: 24px; color: #dc3545;">{{code}}</strong>
            </div>
            <p>If you did not request this password reset, please ignore this email.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getPasswordResetDoneTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">Password Reset Successful</h2>
            <p>Dear {{fullname}},</p>
            <p>Your password has been successfully reset. You can now log in with your new password.</p>
            <p>If you did not make this change, please contact our support team immediately.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getEmailVerificationTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Email Verification Required</h2>
            <p>Dear {{fullname}},</p>
            <p>Thank you for registering with {{site_name}}. Please verify your email address using the code below:</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; margin: 20px 0;">
                <strong style="font-size: 24px; color: #dc3545;">{{code}}</strong>
            </div>
            <p>Welcome to {{site_name}}!</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getSupportReplyTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Support Ticket Reply</h2>
            <p>Dear {{fullname}},</p>
            <p>We have replied to your support ticket. Please check your account for the latest update.</p>
            <p>Thank you for contacting {{site_name}} support.</p>
            <p>Best regards,<br>{{site_name}} Support Team</p>
        </div>';
    }

    private function getDepositCompleteTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">Deposit Completed</h2>
            <p>Dear {{fullname}},</p>
            <p>Your deposit has been successfully completed.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount:</strong> {{amount}} {{currency}}<br>
                <strong>Transaction ID:</strong> {{transaction_id}}
            </div>
            <p>Thank you for using {{site_name}}.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getDepositApproveTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">Deposit Approved</h2>
            <p>Dear {{fullname}},</p>
            <p>Your deposit request has been approved and processed.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount:</strong> {{amount}} {{currency}}<br>
                <strong>Transaction ID:</strong> {{transaction_id}}
            </div>
            <p>The funds have been added to your account.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getDepositRejectTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Deposit Rejected</h2>
            <p>Dear {{fullname}},</p>
            <p>Unfortunately, your deposit request has been rejected.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount:</strong> {{amount}} {{currency}}<br>
                <strong>Reason:</strong> {{reason}}
            </div>
            <p>Please contact our support team if you have any questions.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getWithdrawApproveTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">Withdrawal Approved</h2>
            <p>Dear {{fullname}},</p>
            <p>Your withdrawal request has been approved and processed.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount:</strong> {{amount}} {{currency}}<br>
                <strong>Transaction ID:</strong> {{transaction_id}}
            </div>
            <p>The funds will be transferred to your designated account.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getWithdrawRejectTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Withdrawal Rejected</h2>
            <p>Dear {{fullname}},</p>
            <p>Unfortunately, your withdrawal request has been rejected.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount:</strong> {{amount}} {{currency}}<br>
                <strong>Reason:</strong> {{reason}}
            </div>
            <p>Please contact our support team if you have any questions.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getBalanceAddTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">Balance Added</h2>
            <p>Dear {{fullname}},</p>
            <p>Your account balance has been updated.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount Added:</strong> {{amount}} {{currency}}<br>
                <strong>New Balance:</strong> {{new_balance}} {{currency}}
            </div>
            <p>Thank you for using {{site_name}}.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getBalanceSubtractTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Balance Deducted</h2>
            <p>Dear {{fullname}},</p>
            <p>An amount has been deducted from your account balance.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Amount Deducted:</strong> {{amount}} {{currency}}<br>
                <strong>New Balance:</strong> {{new_balance}} {{currency}}
            </div>
            <p>If you have any questions, please contact our support team.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getKycApproveTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #28a745;">KYC Verification Approved</h2>
            <p>Dear {{fullname}},</p>
            <p>Congratulations! Your KYC verification has been approved on {{date}}.</p>
            <p>You now have full access to all features of your {{site_name}} account.</p>
            <p>Thank you for completing the verification process.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getKycRejectTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">KYC Verification Rejected</h2>
            <p>Dear {{fullname}},</p>
            <p>Unfortunately, your KYC verification was rejected on {{date}}.</p>
            <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <strong>Reason:</strong> {{reason}}
            </div>
            <p>Please resubmit your documents with the necessary corrections.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }

    private function getGenericTemplate(): string
    {
        return '<div style="font-family: Arial, sans-serif; line-height: 1.6;">
            <h2 style="color: #dc3545;">Notification from {{site_name}}</h2>
            <p>Dear {{fullname}},</p>
            <p>This is a notification from {{site_name}}.</p>
            <p>Best regards,<br>{{site_name}} Team</p>
        </div>';
    }
}
