/**
 * Simple Email Template Editor Styles
 * Lightweight replacement for Visual Builder
 * Uses only existing MBFX theme colors (black/red)
 */

/* Main Editor Container */
.simple-email-editor {
    background: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Shortcode Buttons Bar */
.shortcode-buttons-bar {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.shortcode-buttons-bar .shortcode-label {
    font-weight: 600;
    color: #333;
    margin-right: 10px;
    font-size: 14px;
}

.shortcode-btn {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    background: #ffffff;
    border: 1px solid #dc3545;
    color: #dc3545;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    text-decoration: none;
    white-space: nowrap;
}

.shortcode-btn:hover {
    background: #dc3545;
    color: #ffffff;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3);
}

.shortcode-btn i {
    margin-right: 5px;
    font-size: 14px;
}

/* Editor Mode Tabs */
.editor-mode-tabs {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 0 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.editor-tabs {
    display: flex;
    gap: 0;
}

.editor-tab {
    padding: 12px 20px;
    background: transparent;
    border: none;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    border-bottom: 3px solid transparent;
}

.editor-tab:hover {
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.editor-tab.active {
    color: #dc3545;
    border-bottom-color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
}

.editor-actions {
    display: flex;
    gap: 10px;
}

/* Editor Panels */
.editor-panel {
    padding: 20px;
    min-height: 400px;
}

.visual-editor-panel {
    display: block;
}

.html-editor-panel {
    display: none;
}

/* Visual Editor */
.visual-editor-content {
    width: 100%;
    min-height: 400px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    background: #ffffff;
    resize: vertical;
    overflow-y: auto;
    word-wrap: break-word;
    /* Enable full email template rendering */
    position: relative;
}

.visual-editor-content:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Enhanced Email Template Styling within Visual Editor */
.visual-editor-content table {
    border-collapse: collapse;
    width: 100%;
    max-width: 100%;
    margin: 0 auto;
    font-family: Arial, sans-serif;
}

.visual-editor-content table td {
    padding: 8px;
    vertical-align: top;
    border: 0;
}

.visual-editor-content img {
    max-width: 100%;
    height: auto;
    display: block;
    border: 0;
}

.visual-editor-content h1,
.visual-editor-content h2,
.visual-editor-content h3 {
    margin: 10px 0;
    color: #333;
    font-family: Arial, sans-serif;
}

.visual-editor-content p {
    margin: 10px 0;
    color: #333;
    font-family: Arial, sans-serif;
}

.visual-editor-content a {
    color: #dc3545;
    text-decoration: none;
}

.visual-editor-content a:hover {
    text-decoration: underline;
}

/* Email template container styling */
.visual-editor-content [style*="background"] {
    border-radius: 4px;
}

.visual-editor-content [style*="gradient"] {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
}

/* Email template specific styling */
.visual-editor-content .email-header {
    background: #dc3545;
    color: white;
    padding: 20px;
    text-align: center;
}

.visual-editor-content .email-body {
    padding: 20px;
    background: #ffffff;
}

.visual-editor-content .email-footer {
    background: #f8f9fa;
    padding: 15px;
    text-align: center;
    font-size: 12px;
    color: #6c757d;
}

.visual-editor-content .btn {
    display: inline-block;
    padding: 10px 20px;
    background: #dc3545;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    border: none;
    cursor: pointer;
}

.visual-editor-content .btn:hover {
    background: #c82333;
    color: white;
}

/* Preserve inline styles for email templates */
.visual-editor-content [style] {
    /* Allow all inline styles to be preserved */
}

/* Placeholder styling */
.visual-editor-content:empty:before {
    content: attr(placeholder);
    color: #6c757d;
    font-style: italic;
    pointer-events: none;
}

/* Email template preview mode */
.visual-editor-content.preview-mode {
    background: #f8f9fa;
    border: 2px dashed #dee2e6;
}

.visual-editor-content.preview-mode::before {
    content: "Email Template Preview";
    display: block;
    text-align: center;
    padding: 10px;
    background: #dc3545;
    color: white;
    margin: -15px -15px 15px -15px;
    font-weight: bold;
}

/* HTML Editor */
.html-editor-textarea {
    width: 100%;
    min-height: 400px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 13px;
    line-height: 1.5;
    background: #f8f9fa;
    color: #333;
    resize: vertical;
}

.html-editor-textarea:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
    background: #ffffff;
}

/* SMS Editor Section */
.sms-editor-section {
    background: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 20px;
}

.sms-editor-section .form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.sms-editor-textarea {
    width: 100%;
    min-height: 120px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    font-family: Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    background: #ffffff;
    resize: vertical;
}

.sms-editor-textarea:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

/* Form Actions */
.form-actions {
    background: #ffffff;
    border-top: 1px solid #dee2e6;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 15px;
}

.form-actions .btn {
    min-width: 120px;
}

/* Test Email Section */
.test-email-section {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.test-email-input {
    min-width: 200px;
    flex: 1;
}

.test-email-result {
    margin-top: 10px;
    width: 100%;
}

/* Responsive Design */
@media (max-width: 768px) {
    .shortcode-buttons-bar {
        padding: 10px;
    }
    
    .shortcode-btn {
        font-size: 11px;
        padding: 5px 8px;
    }
    
    .editor-mode-tabs {
        flex-direction: column;
        align-items: stretch;
        padding: 10px;
        gap: 10px;
    }
    
    .editor-tabs {
        justify-content: center;
    }
    
    .editor-actions {
        justify-content: center;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .test-email-section {
        flex-direction: column;
        align-items: stretch;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent !important;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notifications */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    font-size: 14px;
    font-weight: 500;
}

/* Success/Error States */
.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #bee5eb;
    color: #0c5460;
}

/* Preview Modal Styles */
.preview-controls {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.preview-mode-buttons {
    display: flex;
    gap: 10px;
}

.preview-mode-buttons .btn {
    border-radius: 5px;
    font-size: 12px;
    padding: 6px 12px;
    transition: all 0.3s ease;
}

.preview-mode-buttons .btn.active {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #fff !important;
}

.preview-container {
    background: #f8f9fa;
    min-height: 600px;
}

.preview-frame {
    padding: 20px;
}

.mobile-frame {
    background: #333;
    padding: 10px;
    border-radius: 25px;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.3);
}

.mobile-frame iframe {
    border-radius: 15px;
}

/* Modal Enhancements */
.modal-xl {
    max-width: 1200px;
}

.modal-header.bg--primary {
    background-color: #dc3545 !important;
    border-bottom: 1px solid #dc3545;
}

.btn-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #fff;
    opacity: 0.8;
}

.btn-close:hover {
    opacity: 1;
}
