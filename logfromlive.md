[2025-06-30 19:06:05] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 19:06:05] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 19:06:23] production.INFO: === TEMPLATE UPDATE DEBUG START ===  
[2025-06-30 19:06:23] production.INFO: Template ID: 36  
[2025-06-30 19:06:23] production.INFO: Request Method: POST  
[2025-06-30 19:06:23] production.INFO: Request URL: https://mbf.mybrokerforex.com/admin/notification/template/update/36  
[2025-06-30 19:06:23] production.INFO: User Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36  
[2025-06-30 19:06:23] production.INFO: Server Environment: WINNT - PHP 8.4.8  
[2025-06-30 19:06:23] production.INFO: Content Type: application/x-www-form-urlencoded  
[2025-06-30 19:06:23] production.INFO: Content Length: 13598  
[2025-06-30 19:06:23] production.INFO: Raw POST data keys: _token, subject, email_status, email_body_final, email_body, template_id, sms_body  
[2025-06-30 19:06:23] production.INFO: Email body field exists: YES  
[2025-06-30 19:06:23] production.INFO: Email body final field exists: YES  
[2025-06-30 19:06:23] production.INFO: ✅ Validation passed  
[2025-06-30 19:06:23] production.INFO: ✅ Template found - Current subject: IB Application Status Update  
[2025-06-30 19:06:23] production.INFO: ✅ Template found - Current email_body length: 3781  
[2025-06-30 19:06:23] production.INFO: ✅ Subject updated to: IB Application Status Update  
[2025-06-30 19:06:23] production.INFO: === SIMPLIFIED EMAIL BODY PROCESSING ===  
[2025-06-30 19:06:23] production.INFO: Email body source: email_body_final  
[2025-06-30 19:06:23] production.INFO: Email body length: 1670  
[2025-06-30 19:06:23] production.INFO: Email body preview (first 200 chars): IB Application Rejected


    
    
        
            
                
                

                    
                    
                        
                             
[2025-06-30 19:06:23] production.INFO: After minimal Windows cleanup - Email body length: 1620  
[2025-06-30 19:06:23] production.INFO: Template 36: Using content directly from editor with minimal cleanup  
[2025-06-30 19:06:23] production.INFO: === FINAL CONTENT READY FOR SAVE ===  
[2025-06-30 19:06:23] production.INFO: Final email body length: 1620  
[2025-06-30 19:06:23] production.INFO: Final email body preview (first 300 chars): IB Application Rejected


    
    
        
            
                
                

                    
                    
                        
                            
                        
                    

                    
                    
                        
[2025-06-30 19:06:23] production.INFO: Template 36: Skipping all corruption detection and complex processing to prevent issues  
[2025-06-30 19:06:23] production.INFO: === DATABASE SAVE OPERATION DEBUG ===  
[2025-06-30 19:06:23] production.INFO: Before save - Template email_body length: 3781  
[2025-06-30 19:06:23] production.INFO: Before save - New email_body length: 1620  
[2025-06-30 19:06:23] production.INFO: Before save - Template dirty: []  
[2025-06-30 19:06:23] production.INFO: After setting fields - Template dirty: {"email_body":"IB Application Rejected\n\n\n    \n    \n        \n            \n                \n                \n\n                    \n                    \n                        \n                            \n                        \n                    \n\n                    \n                    \n                        \n                            IB Application Rejecteded\n                        \n                    \n\n                    \n                    \n                        \n                            We have reviewed your IB application.\n                        \n                    \n\n                    \n                    \n                        \n                            Dear {{fullname}},We have reviewed your IB application and unfortunately cannot approve it at this time.Application Date: {{application_date}}IB Type: {{requested_ib_type}}Review Date: {{review_date}}Rejection Reason: {{rejection_reason}}You may reapply after addressing the mentioned requirements.Thank you for your interest in our IB program.\n                        \n                    \n\n                    \n                    \n                        \n                            Best regards,MBFX Team\n                        \n                    \n\n                    \n                    \n                        \n                            MBFX - Professional Trading Platform\n                            Account Settings | Contact Support | Privacy Policy\n                            &copy; 2025 MBFX. All rights reserved. This email was sent to {{email}}. If you no longer wish to receive these emails, update your preferences."}  
[2025-06-30 19:06:23] production.INFO: After setting fields - Template email_body length: 1620  
[2025-06-30 19:06:23] production.INFO: Save operation result: SUCCESS  
[2025-06-30 19:06:23] production.INFO: After refresh - Template email_body length: 1620  
[2025-06-30 19:06:23] production.INFO: After refresh - Content matches: YES  
[2025-06-30 19:06:23] production.INFO: ✅ Template 36: Database operation completed  
[2025-06-30 19:06:23] production.INFO: === TEMPLATE UPDATE DEBUG END ===  
[2025-06-30 19:06:36] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 19:06:36] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
[2025-06-30 19:07:07] production.INFO: Dashboard activity request {"tab":"transactions","limit":"10"} 
[2025-06-30 19:07:07] production.ERROR: Error loading withdrawals {"error":"SQLSTATE[42S22]: Column not found: 1054 Unknown column 'method_code' in 'field list' (Connection: mysql, SQL: select `id`, `user_id`, `amount`, `charge`, `method_code`, `status`, `created_at`, `updated_at` from `withdrawals` order by `created_at` desc limit 10)"} 
